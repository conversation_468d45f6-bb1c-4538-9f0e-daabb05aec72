package com.jobudget.rentahub.data.models

data class Property(
    val id: String,
    val title: String,
    val description: String,
    val price: Double,
    val address: String,
    val area: Double, // m²
    val bedrooms: Int,
    val bathrooms: Int,
    val images: List<String>,
    val propertyType: PropertyType,
    val rentalType: RentalType,
    val amenities: List<String>,
    val contactInfo: ContactInfo,
    val isAvailable: Boolean = true,
    val createdAt: Long = 0L
)

enum class PropertyType(val displayName: String, val imageUrl: String) {
    APARTMENT(
        "Căn hộ",
        "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    ),
    HOUSE(
        "Nhà ở",
        "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    ),
    ROOM(
        "Phòng trọ",
        "https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
    ),
    STUDIO(
        "Studio",
        "https://images.unsplash.com/photo-1724166647099-7a5b3e686e8f?q=80&w=2671&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    ),
    VILLA(
        "Biệt thự",
        "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    )
}

enum class RentalType(val displayName: String) {
    MONTHLY ("tháng"),
    YEARLY ("năm"),
    DAILY ("ngày")
}

data class ContactInfo(
    val ownerName: String,
    val phoneNumber: String,
    val email: String? = null
)

// Sample data cho demo
object SampleProperties {
    val properties = listOf(
        Property(
            id = "0",
            title = "Căn hộ Akari Tan Binh",
            description = "Căn hộ hiện đại, đầy đủ nội thất, view thành phố tuyệt đẹp",
            price = 15000000.0,
            address = "123 Nguyễn Huệ, Quận 1, TP.HCM",
            area = 80.0,
            bedrooms = 2,
            bathrooms = 2,
            images = listOf(),
            propertyType = PropertyType.APARTMENT,
            rentalType = RentalType.MONTHLY,
            amenities = listOf("Điều hòa", "Tủ lạnh", "Máy giặt", "WiFi", "Bảo vệ 24/7"),
            contactInfo = ContactInfo(
                ownerName = "Anh Minh",
                phoneNumber = "0901234567",
                email = "<EMAIL>"
            )
        ),
        Property(
            id = "1",
            title = "Căn hộ 2 phòng ngủ tại Quận 1",
            description = "Căn hộ hiện đại, đầy đủ nội thất, view thành phố tuyệt đẹp",
            price = 15000000.0,
            address = "123 Nguyễn Huệ, Quận 1, TP.HCM",
            area = 80.0,
            bedrooms = 2,
            bathrooms = 2,
            images = listOf(
                "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1505691938895-1758d7feb511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1505691938895-1758d7feb511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1505691938895-1758d7feb511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1505691938895-1758d7feb511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1505691938895-1758d7feb511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
            ),
            propertyType = PropertyType.APARTMENT,
            rentalType = RentalType.MONTHLY,
            amenities = listOf("Điều hòa", "Tủ lạnh", "Máy giặt", "WiFi", "Bảo vệ 24/7"),
            contactInfo = ContactInfo(
                ownerName = "Anh Minh",
                phoneNumber = "0901234567",
                email = "<EMAIL>"
            )
        ),
        Property(
            id = "2",
            title = "Phòng trọ sinh viên gần ĐH Bách Khoa",
            description = "Phòng trọ sạch sẽ, an ninh tốt, gần trường học",
            price = 3500000.0,
            address = "456 Lý Thường Kiệt, Quận 10, TP.HCM",
            area = 25.0,
            bedrooms = 1,
            bathrooms = 1,
            images = listOf(
                "https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
            ),
            propertyType = PropertyType.ROOM,
            rentalType = RentalType.MONTHLY,
            amenities = listOf("Điều hòa", "WiFi", "Giờ giấc tự do"),
            contactInfo = ContactInfo(
                ownerName = "Chị Lan",
                phoneNumber = "0987654321"
            )
        ),
        Property(
            id = "3",
            title = "Nhà nguyên căn 3 phòng ngủ",
            description = "Nhà mới xây, có sân vườn, phù hợp gia đình",
            price = 25000000.0,
            address = "789 Hoàng Văn Thụ, Quận Tân Bình, TP.HCM",
            area = 120.0,
            bedrooms = 3,
            bathrooms = 3,
            images = listOf(
                "https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
                "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
            ),
            propertyType = PropertyType.HOUSE,
            rentalType = RentalType.MONTHLY,
            amenities = listOf("Sân vườn", "Chỗ đậu xe", "Điều hòa", "Tủ lạnh"),
            contactInfo = ContactInfo(
                ownerName = "Anh Tuấn",
                phoneNumber = "0912345678",
                email = "<EMAIL>"
            )
        )
    )
}
