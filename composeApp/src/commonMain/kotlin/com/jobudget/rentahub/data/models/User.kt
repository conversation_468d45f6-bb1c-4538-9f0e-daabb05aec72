package com.jobudget.rentahub.data.models

data class User(
    val id: String,
    val name: String,
    val email: String,
    val phoneNumber: String,
    val avatar: String? = null,
    val userType: UserType,
    val createdAt: Long = 0L
)

enum class UserType {
    TENANT,    // Người thuê
    LANDLORD   // Người cho thuê
}

// Sample user cho demo
object SampleUser {
    val currentUser = User(
        id = "user1",
        name = "Nguyễn Văn A",
        email = "<EMAIL>",
        phoneNumber = "0901234567",
        userType = UserType.TENANT
    )
}
