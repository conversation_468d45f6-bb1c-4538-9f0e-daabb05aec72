package com.jobudget.rentahub.ui.theme

import androidx.compose.ui.unit.dp

// Airbnb-inspired spacing system
object RentaHubSpacing {
    // Base spacing unit (4dp)
    val unit = 4.dp
    
    // Micro spacing
    val xs = 4.dp    // 1 unit
    val sm = 8.dp    // 2 units
    val md = 12.dp   // 3 units
    val lg = 16.dp   // 4 units
    val xl = 20.dp   // 5 units
    val xxl = 24.dp  // 6 units
    val xxxl = 32.dp // 8 units
    
    // Semantic spacing
    val tiny = xs
    val small = sm
    val medium = lg
    val large = xxl
    val extraLarge = xxxl
    
    // Component-specific spacing
    object Card {
        val padding = lg
        val margin = md
        val gap = sm
    }
    
    object Screen {
        val horizontal = lg
        val vertical = lg
        val top = xxl
        val bottom = lg
    }
    
    object List {
        val itemSpacing = md
        val sectionSpacing = xxl
    }
    
    object Button {
        val paddingHorizontal = lg
        val paddingVertical = md
        val gap = sm
    }
    
    object TextField {
        val paddingHorizontal = lg
        val paddingVertical = md
    }
    
    object Navigation {
        val padding = md
        val iconSpacing = sm
    }
    
    object Property {
        val cardPadding = lg
        val imageSpacing = sm
        val infoSpacing = sm
        val priceSpacing = xs
    }
}
