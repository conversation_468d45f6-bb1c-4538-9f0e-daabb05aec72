package com.jobudget.rentahub.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bed
import androidx.compose.material.icons.filled.Bathtub
import androidx.compose.material.icons.filled.SquareFoot
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.jobudget.rentahub.data.models.Property
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.ui.theme.rentaHub

@Composable
fun PropertyCard(
    property: Property,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val theme = MaterialTheme.rentaHub

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .shadow(
                elevation = 2.dp,
                shape = theme.customShapes.PropertyCard,
                spotColor = Color.Black.copy(alpha = 0.1f)
            ),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.CardBackground
        ),
        shape = theme.customShapes.PropertyCard,
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
//            modifier = Modifier.padding(theme.spacing.medium)
        ) {
            // Hình ảnh placeholder với style Airbnb
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .clip(theme.customShapes.ImageContainer)
                    .background(theme.colors.Gray100),
                contentAlignment = Alignment.Center
            ) {
                if (property.images.isNotEmpty()) {
                    AsyncImage(property.images[0], contentDescription = null)
                }
            }

            Spacer(modifier = Modifier.height(theme.spacing.small))

            // Địa chỉ (Airbnb style - địa chỉ trước tiêu đề)
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = "Địa chỉ",
                    tint = theme.colors.Gray500,
                    modifier = Modifier.size(14.dp)
                )
                Spacer(modifier = Modifier.width(theme.spacing.xs))
                Text(
                    text = property.address.split(",").take(2).joinToString(", "),
                    style = theme.textStyles.PropertySubtitle,
                    color = theme.colors.Gray500,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(theme.spacing.xs))

            // Tiêu đề (Airbnb style - ngắn gọn hơn)
            Text(
                text = property.title,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold,
                    color = theme.colors.OnSurface
                ),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(theme.spacing.xs))

            // Rating và reviews (Airbnb style)
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = "Rating",
                    tint = theme.colors.Rating,
                    modifier = Modifier.size(14.dp)
                )
                Spacer(modifier = Modifier.width(theme.spacing.xs))
                Text(
                    text = "4.8 (127 đánh giá)",
                    style = theme.textStyles.PropertySubtitle,
                    color = theme.colors.Gray600
                )
            }

            Spacer(modifier = Modifier.height(theme.spacing.small))

            // Thông tin chi tiết (Airbnb style - compact)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(theme.spacing.md)
            ) {
                PropertyInfoChip(
                    icon = Icons.Default.Bed,
                    text = "${property.bedrooms} PN",
                    theme = theme
                )
                PropertyInfoChip(
                    icon = Icons.Default.Bathtub,
                    text = "${property.bathrooms} WC",
                    theme = theme
                )
                PropertyInfoChip(
                    icon = Icons.Default.SquareFoot,
                    text = "${property.area.toInt()}m²",
                    theme = theme
                )
            }

            Spacer(modifier = Modifier.height(theme.spacing.small))

            // Giá (Airbnb style - prominent)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = formatPrice(property.price),
                        style = theme.textStyles.PriceDisplay.copy(
                            color = theme.colors.OnSurface
                        )
                    )
                    Text(
                        text = "/${getRentalTypeText(property.rentalType)}",
                        style = theme.textStyles.PropertySubtitle,
                        color = theme.colors.Gray600,
                        modifier = Modifier.padding(start = theme.spacing.xs)
                    )
                }

                // Property type tag
                Surface(
                    color = theme.colors.Gray100,
                    shape = theme.customShapes.Tag
                ) {
                    Text(
                        text = getPropertyTypeText(property.propertyType),
                        modifier = Modifier.padding(
                            horizontal = theme.spacing.sm,
                            vertical = theme.spacing.xs
                        ),
                        style = theme.textStyles.Caption,
                        color = theme.colors.Gray700
                    )
                }
            }
        }
    }
}

@Composable
private fun PropertyInfoChip(
    icon: ImageVector,
    text: String,
    theme: com.jobudget.rentahub.ui.theme.RentaHubTheme
) {
    Surface(
        color = theme.colors.Gray50,
        shape = theme.customShapes.Chip,
        modifier = Modifier
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(
                horizontal = theme.spacing.sm,
                vertical = theme.spacing.xs
            )
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = theme.colors.Gray600,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(theme.spacing.xs))
            Text(
                text = text,
                style = theme.textStyles.Caption,
                color = theme.colors.Gray700
            )
        }
    }
}

private fun getPropertyTypeText(type: PropertyType): String {
    return when (type) {
        PropertyType.APARTMENT -> "Căn hộ"
        PropertyType.HOUSE -> "Nhà"
        PropertyType.ROOM -> "Phòng trọ"
        PropertyType.STUDIO -> "Studio"
        PropertyType.VILLA -> "Biệt thự"
    }
}

private fun getRentalTypeText(type: com.jobudget.rentahub.data.models.RentalType): String {
    return when (type) {
        com.jobudget.rentahub.data.models.RentalType.MONTHLY -> "tháng"
        com.jobudget.rentahub.data.models.RentalType.YEARLY -> "năm"
        com.jobudget.rentahub.data.models.RentalType.DAILY -> "ngày"
    }
}

private fun formatPrice(price: Double): String {
    // Simple formatting for multiplatform compatibility
    val priceString = price.toLong().toString()
    val reversed = priceString.reversed()
    val formatted = reversed.chunked(3).joinToString(".").reversed()
    return "$formatted ₫"
}
