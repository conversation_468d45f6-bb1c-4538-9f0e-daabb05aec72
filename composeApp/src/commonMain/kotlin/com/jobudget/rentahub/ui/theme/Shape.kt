package com.jobudget.rentahub.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

// Airbnb-inspired shapes
val RentaHubShapes = Shapes(
    extraSmall = RoundedCornerShape(4.dp),
    small = RoundedCornerShape(8.dp),
    medium = RoundedCornerShape(12.dp),
    large = RoundedCornerShape(16.dp),
    extraLarge = RoundedCornerShape(24.dp)
)

// Custom shapes for specific components
object RentaHubCustomShapes {
    // Property cards - slightly rounded like Airbnb
    val PropertyCard = RoundedCornerShape(12.dp)
    
    // Image containers
    val ImageContainer = RoundedCornerShape(8.dp)
    val ImageContainerLarge = RoundedCornerShape(16.dp)
    
    // Buttons
    val ButtonPrimary = RoundedCornerShape(8.dp)
    val ButtonSecondary = RoundedCornerShape(8.dp)
    val ButtonRounded = RoundedCornerShape(24.dp)
    
    // Input fields
    val TextField = RoundedCornerShape(8.dp)
    
    // Bottom sheets and modals
    val BottomSheet = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
    
    // Chips and tags
    val Chip = RoundedCornerShape(16.dp)
    val Tag = RoundedCornerShape(6.dp)
    
    // Search bar
    val SearchBar = RoundedCornerShape(24.dp)
    
    // Dialog
    val Dialog = RoundedCornerShape(16.dp)
    
    // Navigation bar
    val NavigationBar = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
}
