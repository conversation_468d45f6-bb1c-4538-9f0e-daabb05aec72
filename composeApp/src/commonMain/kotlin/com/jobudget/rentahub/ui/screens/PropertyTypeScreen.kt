package com.jobudget.rentahub.ui.screens

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.scaleIn
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.jobudget.rentahub.data.models.Property
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.data.models.SampleProperties
import com.jobudget.rentahub.ui.components.PropertyCard
import com.jobudget.rentahub.ui.theme.RentaHubColors
import com.jobudget.rentahub.ui.theme.rentaHub
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class, ExperimentalSharedTransitionApi::class)
@Composable
fun PropertyTypeScreen(
  propertyType: PropertyType,
  onNavigateBack: () -> Unit,
  onNavigateToPropertyDetail: (String) -> Unit,
  sharedTransitionScope: SharedTransitionScope,
  animatedContentScope: AnimatedContentScope
) {
  val theme = MaterialTheme.rentaHub

  // Filter properties by type
  val filteredProperties = remember(propertyType) {
    SampleProperties.properties.filter { it.propertyType == propertyType }
  }

  // Animation state for staggered animation
  var isVisible by remember { mutableStateOf(false) }

  LaunchedEffect(Unit) {
    delay(300) // Delay để hero animation hoàn thành trước
    isVisible = true
  }

  Scaffold(
    topBar = {
      TopAppBar(
        title = {
          Text(
            text = propertyType.displayName,
            style = MaterialTheme.typography.headlineSmall.copy(
              fontWeight = FontWeight.Bold,
              color = theme.colors.OnBackground
            )
          )
        },
        navigationIcon = {
          IconButton(onClick = onNavigateBack) {
            Icon(
              imageVector = Icons.AutoMirrored.Filled.ArrowBack,
              contentDescription = "Quay lại",
              tint = theme.colors.OnBackground
            )
          }
        },
        colors = TopAppBarDefaults.topAppBarColors(
          containerColor = theme.colors.Surface
        ),

        )
    },
    containerColor = theme.colors.Surface
  ) { paddingValues ->
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
//        .padding(paddingValues)
    ) {
      // Hero image section với shared element animation
      item {
        Box(
          modifier = Modifier
            .fillMaxWidth()
            .height(450.dp)
        ) {
          with(sharedTransitionScope) {
            AsyncImage(
              model = propertyType.imageUrl,
              contentDescription = propertyType.displayName,
              modifier = Modifier
                .fillMaxSize()
                .sharedElement(
                  sharedContentState = rememberSharedContentState(key = "property_type_image_${propertyType.name}"),
                  animatedVisibilityScope = animatedContentScope
                ),
              contentScale = ContentScale.Crop
            )
          }

          // Gradient overlay
          with(sharedTransitionScope) {
            Box(
              modifier = Modifier
                .fillMaxSize()
                .background(
                  Brush.verticalGradient(
                    listOf(
                      Color.Transparent,
                      Color.Black.copy(alpha = 0.6f)
                    )
                  )
                )
                .sharedElement(
                  sharedContentState = rememberSharedContentState(key = "property_type_gradient_${propertyType.name}"),
                  animatedVisibilityScope = animatedContentScope
                )
            )
          }

          // Title overlay
          with(sharedTransitionScope) {
            Text(
              text = propertyType.displayName,
              style = MaterialTheme.typography.headlineMedium.copy(
                fontWeight = FontWeight.Bold,
                color = Color.White
              ),
              modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(theme.spacing.medium)
                .sharedElement(
                  sharedContentState = rememberSharedContentState(key = "property_type_title_${propertyType.name}"),
                  animatedVisibilityScope = animatedContentScope
                ),
            )
          }
        }
      }

      item {
        Spacer(modifier = Modifier.height(theme.spacing.medium))
      }

      // Properties count
      item {
        Text(
          text = "${filteredProperties.size} bất động sản",
          style = MaterialTheme.typography.titleMedium.copy(
            fontWeight = FontWeight.SemiBold,
            color = theme.colors.OnBackground
          ),
          modifier = Modifier.padding(horizontal = theme.spacing.medium)
        )
      }

      item {
        Spacer(modifier = Modifier.height(theme.spacing.medium))
      }

      itemsIndexed(filteredProperties) { index, property ->
        AnimatedVisibility(
          visible = isVisible,
          modifier = Modifier.padding(horizontal = theme.spacing.medium).padding(
            bottom = theme.spacing.medium
          ),
          enter = fadeIn(
            animationSpec = tween(
              durationMillis = 300,
              delayMillis = index * 100 // Staggered delay
            )
          ) + scaleIn(
            animationSpec = spring(
              dampingRatio = Spring.DampingRatioMediumBouncy,
              stiffness = Spring.StiffnessLow
            ),
            initialScale = 0.8f
          )
        ) {
          PropertyCard(
            property = property,
            onClick = { onNavigateToPropertyDetail(property.id) },
            modifier = Modifier.fillMaxWidth()
          )
        }
      }

    }
  }
}


