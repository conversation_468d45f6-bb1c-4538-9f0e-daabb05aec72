package com.jobudget.rentahub.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.scaleIn
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.jobudget.rentahub.data.models.Property
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.data.models.SampleProperties
import com.jobudget.rentahub.ui.components.PropertyCard
import com.jobudget.rentahub.ui.theme.RentaHubColors
import com.jobudget.rentahub.ui.theme.rentaHub
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PropertyTypeScreen(
    propertyType: PropertyType,
    onNavigateBack: () -> Unit,
    onNavigateToPropertyDetail: (String) -> Unit
) {
    val theme = MaterialTheme.rentaHub
    
    // Filter properties by type
    val filteredProperties = remember(propertyType) {
        SampleProperties.properties.filter { it.propertyType == propertyType }
    }
    
    // Animation state for staggered animation
    var isVisible by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(300) // Delay để hero animation hoàn thành trước
        isVisible = true
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = getPropertyTypeDisplayName(propertyType),
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold,
                            color = theme.colors.OnBackground
                        )
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Quay lại",
                            tint = theme.colors.OnBackground
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = theme.colors.Surface
                )
            )
        },
        containerColor = theme.colors.Surface
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Hero image section với shared element animation
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(250.dp)
                    .padding(horizontal = theme.spacing.medium)
                    .clip(theme.customShapes.PropertyCard)
            ) {
                AsyncImage(
                    model = getPropertyTypeImageUrl(propertyType),
                    contentDescription = getPropertyTypeDisplayName(propertyType),
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
                
                // Gradient overlay
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                listOf(
                                    Color.Transparent,
                                    Color.Black.copy(alpha = 0.6f)
                                )
                            )
                        )
                )
                
                // Title overlay
                Text(
                    text = getPropertyTypeDisplayName(propertyType),
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    ),
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .padding(theme.spacing.medium)
                )
            }
            
            Spacer(modifier = Modifier.height(theme.spacing.medium))
            
            // Properties count
            Text(
                text = "${filteredProperties.size} bất động sản",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold,
                    color = theme.colors.OnBackground
                ),
                modifier = Modifier.padding(horizontal = theme.spacing.medium)
            )
            
            Spacer(modifier = Modifier.height(theme.spacing.medium))
            
            // Properties grid với staggered animation
            LazyVerticalGrid(
                columns = GridCells.Fixed(1),
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(horizontal = theme.spacing.medium),
                verticalArrangement = Arrangement.spacedBy(theme.spacing.medium)
            ) {
                itemsIndexed(filteredProperties) { index, property ->
                    AnimatedVisibility(
                        visible = isVisible,
                        enter = fadeIn(
                            animationSpec = tween(
                                durationMillis = 300,
                                delayMillis = index * 100 // Staggered delay
                            )
                        ) + scaleIn(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessLow
                            ),
                            initialScale = 0.8f
                        )
                    ) {
                        PropertyCard(
                            property = property,
                            onClick = { onNavigateToPropertyDetail(property.id) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}

private fun getPropertyTypeDisplayName(type: PropertyType): String {
    return when (type) {
        PropertyType.APARTMENT -> "Căn hộ"
        PropertyType.HOUSE -> "Nhà ở"
        PropertyType.ROOM -> "Phòng trọ"
        PropertyType.STUDIO -> "Studio"
        PropertyType.VILLA -> "Biệt thự"
    }
}

private fun getPropertyTypeImageUrl(type: PropertyType): String {
    return when (type) {
        PropertyType.APARTMENT -> "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        PropertyType.HOUSE -> "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        PropertyType.ROOM -> "https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
        PropertyType.STUDIO -> "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80"
        PropertyType.VILLA -> "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1171&q=80"
    }
}
