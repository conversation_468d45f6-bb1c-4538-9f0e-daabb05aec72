package com.jobudget.rentahub.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf

// Custom theme data class
data class RentaHubTheme(
    val colors: RentaHubColors,
    val spacing: RentaHubSpacing,
    val customShapes: RentaHubCustomShapes,
    val textStyles: RentaHubTextStyles
)

// Composition locals for custom theme
val LocalRentaHubTheme = staticCompositionLocalOf {
    RentaHubTheme(
        colors = RentaHubColors,
        spacing = RentaHubSpacing,
        customShapes = RentaHubCustomShapes,
        textStyles = RentaHubTextStyles
    )
}

@Composable
fun RentaHubTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        DarkColorScheme
    } else {
        LightColorScheme
    }
    
    val customTheme = RentaHubTheme(
        colors = RentaHubColors,
        spacing = RentaHubSpacing,
        customShapes = RentaHubCustomShapes,
        textStyles = RentaHubTextStyles
    )
    
    CompositionLocalProvider(
        LocalRentaHubTheme provides customTheme
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = RentaHubTypography,
            shapes = RentaHubShapes,
            content = content
        )
    }
}

// Extension property to access custom theme
val MaterialTheme.rentaHub: RentaHubTheme
    @Composable
    get() = LocalRentaHubTheme.current
