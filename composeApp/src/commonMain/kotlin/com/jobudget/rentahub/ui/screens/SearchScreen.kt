package com.jobudget.rentahub.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.jobudget.rentahub.data.models.Property
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.data.models.SampleProperties
import com.jobudget.rentahub.ui.components.PropertyCard

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchScreen(
    onNavigateToPropertyDetail: (String) -> Unit
) {
    var searchQuery by remember { mutableStateOf("") }
    var selectedPropertyType by remember { mutableStateOf<PropertyType?>(null) }
    var minPrice by remember { mutableStateOf("") }
    var maxPrice by remember { mutableStateOf("") }
    var showFilters by remember { mutableStateOf(false) }

    // Filter properties based on search criteria
    val filteredProperties = remember(searchQuery, selectedPropertyType, minPrice, maxPrice) {
        SampleProperties.properties.filter { property ->
            val matchesQuery = if (searchQuery.isBlank()) true else {
                property.title.contains(searchQuery, ignoreCase = true) ||
                property.address.contains(searchQuery, ignoreCase = true) ||
                property.description.contains(searchQuery, ignoreCase = true)
            }

            val matchesType = selectedPropertyType?.let { property.propertyType == it } ?: true

            val matchesPrice = try {
                val min = minPrice.toDoubleOrNull() ?: 0.0
                val max = maxPrice.toDoubleOrNull() ?: Double.MAX_VALUE
                property.price >= min && property.price <= max
            } catch (e: Exception) {
                true
            }

            matchesQuery && matchesType && matchesPrice
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Tìm kiếm",
                        fontWeight = FontWeight.Bold
                    )
                },
                actions = {
                    IconButton(onClick = { showFilters = !showFilters }) {
                        Icon(
                            imageVector = Icons.Default.FilterList,
                            contentDescription = "Bộ lọc"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // Search bar
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                label = { Text("Tìm kiếm theo tên, địa chỉ...") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Filters section
            AnimatedVisibility (showFilters) {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Bộ lọc",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        // Property type filter
                        Text(
                            text = "Loại bất động sản",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth().horizontalScroll(
                                rememberScrollState()
                            ),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            PropertyType.entries.forEach { type ->
                                FilterChip(
                                    selected = selectedPropertyType == type,
                                    onClick = {
                                        selectedPropertyType = if (selectedPropertyType == type) null else type
                                    },
                                    label = { Text(getPropertyTypeText(type)) }
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        // Price range filter
                        Text(
                            text = "Khoảng giá (VNĐ)",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            OutlinedTextField(
                                value = minPrice,
                                maxLines = 1,
                                onValueChange = { minPrice = it },
                                label = { Text("Từ") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier.weight(1f)
                            )

                            OutlinedTextField(
                                value = maxPrice,
                                maxLines = 1,
                                onValueChange = { maxPrice = it },
                                label = { Text("Đến") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier.weight(1f),
                                keyboardActions = KeyboardActions {
                                    showFilters = false

                                }
                            )
                        }

                        Spacer(modifier = Modifier.height(12.dp))

                        // Clear filters button
                        TextButton(
                            onClick = {
                                selectedPropertyType = null
                                minPrice = ""
                                maxPrice = ""
                            }
                        ) {
                            Text("Xóa bộ lọc")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }

            // Results
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Kết quả (${filteredProperties.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Properties list
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(bottom = 16.dp)
            ) {
                items(filteredProperties) { property ->
                    PropertyCard(
                        property = property,
                        onClick = { onNavigateToPropertyDetail(property.id) }
                    )
                }

                if (filteredProperties.isEmpty()) {
                    item {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Không tìm thấy kết quả",
                                style = MaterialTheme.typography.titleMedium
                            )
                            Text(
                                text = "Hãy thử thay đổi từ khóa hoặc bộ lọc",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

private fun getPropertyTypeText(type: PropertyType): String {
    return when (type) {
        PropertyType.APARTMENT -> "Căn hộ"
        PropertyType.HOUSE -> "Nhà"
        PropertyType.ROOM -> "Phòng trọ"
        PropertyType.STUDIO -> "Studio"
        PropertyType.VILLA -> "Biệt thự"
    }
}
