package com.jobudget.rentahub.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.data.models.RentalType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostPropertyScreen(
    onNavigateBack: () -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var address by remember { mutableStateOf("") }
    var price by remember { mutableStateOf("") }
    var area by remember { mutableStateOf("") }
    var bedrooms by remember { mutableStateOf("") }
    var bathrooms by remember { mutableStateOf("") }
    var selectedPropertyType by remember { mutableStateOf<PropertyType?>(null) }
    var selectedRentalType by remember { mutableStateOf<RentalType?>(null) }
    var amenities by remember { mutableStateOf("") }
    var ownerName by remember { mutableStateOf("") }
    var phoneNumber by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    
    var showPropertyTypeDropdown by remember { mutableStateOf(false) }
    var showRentalTypeDropdown by remember { mutableStateOf(false) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Đăng tin cho thuê") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Quay lại")
                    }
                }
            )
        },
        bottomBar = {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shadowElevation = 8.dp
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onNavigateBack,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Hủy")
                    }
                    
                    Button(
                        onClick = { 
                            // Handle post property
                            // Validate and save property
                            onNavigateBack()
                        },
                        modifier = Modifier.weight(1f),
                        enabled = title.isNotBlank() && 
                                 description.isNotBlank() && 
                                 address.isNotBlank() && 
                                 price.isNotBlank() &&
                                 selectedPropertyType != null &&
                                 selectedRentalType != null
                    ) {
                        Icon(Icons.Default.Add, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Đăng tin")
                    }
                }
            }
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Text(
                    text = "Thông tin cơ bản",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // Title
            item {
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("Tiêu đề *") },
                    placeholder = { Text("VD: Căn hộ 2 phòng ngủ tại Quận 1") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
            
            // Description
            item {
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Mô tả *") },
                    placeholder = { Text("Mô tả chi tiết về bất động sản...") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3,
                    maxLines = 5
                )
            }
            
            // Address
            item {
                OutlinedTextField(
                    value = address,
                    onValueChange = { address = it },
                    label = { Text("Địa chỉ *") },
                    placeholder = { Text("Số nhà, đường, quận/huyện, thành phố") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
            
            item {
                Text(
                    text = "Chi tiết bất động sản",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // Property type
            item {
                ExposedDropdownMenuBox(
                    expanded = showPropertyTypeDropdown,
                    onExpandedChange = { showPropertyTypeDropdown = it }
                ) {
                    OutlinedTextField(
                        value = selectedPropertyType?.let { getPropertyTypeText(it) } ?: "",
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Loại bất động sản *") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = showPropertyTypeDropdown) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = showPropertyTypeDropdown,
                        onDismissRequest = { showPropertyTypeDropdown = false }
                    ) {
                        PropertyType.values().forEach { type ->
                            DropdownMenuItem(
                                text = { Text(getPropertyTypeText(type)) },
                                onClick = {
                                    selectedPropertyType = type
                                    showPropertyTypeDropdown = false
                                }
                            )
                        }
                    }
                }
            }
            
            // Rental type
            item {
                ExposedDropdownMenuBox(
                    expanded = showRentalTypeDropdown,
                    onExpandedChange = { showRentalTypeDropdown = it }
                ) {
                    OutlinedTextField(
                        value = selectedRentalType?.let { getRentalTypeText(it) } ?: "",
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Hình thức thuê *") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = showRentalTypeDropdown) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = showRentalTypeDropdown,
                        onDismissRequest = { showRentalTypeDropdown = false }
                    ) {
                        RentalType.values().forEach { type ->
                            DropdownMenuItem(
                                text = { Text(getRentalTypeText(type)) },
                                onClick = {
                                    selectedRentalType = type
                                    showRentalTypeDropdown = false
                                }
                            )
                        }
                    }
                }
            }
            
            // Price and area
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedTextField(
                        value = price,
                        onValueChange = { price = it },
                        label = { Text("Giá thuê (VNĐ) *") },
                        placeholder = { Text("15000000") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                    
                    OutlinedTextField(
                        value = area,
                        onValueChange = { area = it },
                        label = { Text("Diện tích (m²)") },
                        placeholder = { Text("80") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                }
            }
            
            // Bedrooms and bathrooms
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedTextField(
                        value = bedrooms,
                        onValueChange = { bedrooms = it },
                        label = { Text("Số phòng ngủ") },
                        placeholder = { Text("2") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                    
                    OutlinedTextField(
                        value = bathrooms,
                        onValueChange = { bathrooms = it },
                        label = { Text("Số phòng tắm") },
                        placeholder = { Text("2") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                }
            }
            
            // Amenities
            item {
                OutlinedTextField(
                    value = amenities,
                    onValueChange = { amenities = it },
                    label = { Text("Tiện ích") },
                    placeholder = { Text("Điều hòa, WiFi, Tủ lạnh (cách nhau bằng dấu phẩy)") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 3
                )
            }
            
            item {
                Text(
                    text = "Thông tin liên hệ",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // Owner name
            item {
                OutlinedTextField(
                    value = ownerName,
                    onValueChange = { ownerName = it },
                    label = { Text("Tên chủ nhà") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
            
            // Phone number
            item {
                OutlinedTextField(
                    value = phoneNumber,
                    onValueChange = { phoneNumber = it },
                    label = { Text("Số điện thoại") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
            
            // Email
            item {
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text("Email (tùy chọn)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
            
            // Note
            item {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Lưu ý:",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "• Vui lòng cung cấp thông tin chính xác và đầy đủ\n" +
                                  "• Tin đăng sẽ được kiểm duyệt trước khi hiển thị\n" +
                                  "• Bạn có thể chỉnh sửa tin đăng sau khi đăng",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

private fun getPropertyTypeText(type: PropertyType): String {
    return when (type) {
        PropertyType.APARTMENT -> "Căn hộ"
        PropertyType.HOUSE -> "Nhà"
        PropertyType.ROOM -> "Phòng trọ"
        PropertyType.STUDIO -> "Studio"
        PropertyType.VILLA -> "Biệt thự"
    }
}

private fun getRentalTypeText(type: RentalType): String {
    return when (type) {
        RentalType.MONTHLY -> "Theo tháng"
        RentalType.YEARLY -> "Theo năm"
        RentalType.DAILY -> "Theo ngày"
    }
}
