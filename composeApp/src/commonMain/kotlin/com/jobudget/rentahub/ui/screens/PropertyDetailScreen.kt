package com.jobudget.rentahub.ui.screens

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Bed
import androidx.compose.material.icons.filled.Bathtub
import androidx.compose.material.icons.filled.CalendarMonth
import androidx.compose.material.icons.filled.SquareFoot
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material3.*
import androidx.compose.material3.carousel.HorizontalMultiBrowseCarousel
import androidx.compose.material3.carousel.rememberCarouselState
import androidx.compose.runtime.*
import kotlinx.coroutines.delay
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.jobudget.rentahub.data.models.Property
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.data.models.SampleProperties
import com.jobudget.rentahub.ui.theme.RentaHubColors
import com.jobudget.rentahub.ui.theme.RentaHubTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PropertyDetailScreen(
  propertyId: String,
  onNavigateBack: () -> Unit
) {
  println("PropertyDetailScreen: $propertyId")
  val property by remember(propertyId) {
    derivedStateOf { SampleProperties.properties.find { it.id == propertyId } }
  }

  // State để control việc hiển thị bottom bar
  var showBottomBar by remember { mutableStateOf(false) }

  // LaunchedEffect để delay hiển thị bottom bar
  LaunchedEffect(propertyId) {
    delay(1000) // Delay 1 giây
    showBottomBar = true
  }




  if (property == null) {
    // Handle property not found
    Scaffold(
      topBar = {
        TopAppBar(
          title = { Text("Không tìm thấy") },
          navigationIcon = {
            IconButton(onClick = onNavigateBack) {
              Icon(Icons.Default.ArrowBack, contentDescription = "Quay lại")
            }
          }
        )
      }
    ) { paddingValues ->
      Box(
        modifier = Modifier
          .fillMaxSize()
          .padding(paddingValues),
        contentAlignment = Alignment.Center
      ) {
        Text("Không tìm thấy bất động sản")
      }
    }
    return
  }

  Scaffold(
    topBar = {
      TopAppBar(
        title = { Text("Chi tiết") },
        navigationIcon = {
          IconButton(onClick = onNavigateBack) {
            Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Quay lại")
          }
        }
      )
    },
    bottomBar = {
      AnimatedVisibility(
        visible = showBottomBar,
        enter = slideInVertically(
          initialOffsetY = { it } // Slide in từ dưới lên
        ),
        exit = slideOutVertically(
          targetOffsetY = { it } // Slide out xuống dưới
        )
      ) {
        Surface(
          modifier = Modifier.fillMaxWidth(),
          shadowElevation = 8.dp
        ) {
          Row(
            modifier = Modifier
              .fillMaxWidth()
              .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
          ) {
            OutlinedButton(
              onClick = { /* Handle call */ },
              modifier = Modifier.weight(1f)
            ) {
              Icon(Icons.Default.CalendarMonth, contentDescription = null)
              Spacer(modifier = Modifier.width(8.dp))
              Text("Đặt hẹn")
            }

            Button(
              onClick = { /* Handle contact */ },
              modifier = Modifier.weight(1f)
            ) {
              Text("Đặt thuê")
            }
          }
        }
      }
    },
    containerColor = RentaHubColors.Surface
  ) { paddingValues ->
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues),
      contentPadding = PaddingValues(16.dp),
      verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
      // Image gallery
      item {
        HorizontalMultiBrowseCarousel(
          state =  rememberCarouselState { property!!.images.size },
          preferredItemWidth = 300.dp,
          itemSpacing = 16.dp
        ) {
            Box(
              modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant),
              contentAlignment = Alignment.Center
            ) {
              AsyncImage(property!!.images[it], contentDescription = null)
            }
          }

      }

      // Title and price
      item {
        Column {
          Text(
            text = property!!.title,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
          )
          Spacer(modifier = Modifier.height(8.dp))
          Text(
            text = buildAnnotatedString {
              append(formatPrice(property!!.price))
              withStyle(
                SpanStyle(
                  color = MaterialTheme.colorScheme.onSurfaceVariant,
                  fontSize =  18.sp,
                  fontWeight = FontWeight.Medium
                ),
              ) {
                append(" /${getRentalTypeText(property!!.rentalType)}")
              }
            },
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
          )

        }
      }

      // Location
      item {
        Row(
          verticalAlignment = Alignment.CenterVertically
        ) {
          Icon(
            imageVector = Icons.Default.LocationOn,
            contentDescription = "Địa chỉ",
            tint = MaterialTheme.colorScheme.primary
          )
          Spacer(modifier = Modifier.width(8.dp))
          Text(
            text = property!!.address,
            style = MaterialTheme.typography.bodyLarge
          )
        }
      }

      // Property details
      item {
        Card {
          Column(
            modifier = Modifier.padding(16.dp)
          ) {
            Text(
              text = "Thông tin chi tiết",
              style = MaterialTheme.typography.titleMedium,
              fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(12.dp))

            Row(
              modifier = Modifier.fillMaxWidth(),
              horizontalArrangement = Arrangement.SpaceEvenly
            ) {
              DetailItem(
                icon = Icons.Default.Bed,
                label = "Phòng ngủ",
                value = "${property!!.bedrooms}"
              )
              DetailItem(
                icon = Icons.Default.Bathtub,
                label = "Phòng tắm",
                value = "${property!!.bathrooms}"
              )
              DetailItem(
                icon = Icons.Default.SquareFoot,
                label = "Diện tích",
                value = "${property!!.area.toInt()}m²"
              )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row {
              Text(
                text = "Loại: ",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
              )
              Text(
                text = getPropertyTypeText(property!!.propertyType),
                style = MaterialTheme.typography.bodyMedium
              )
            }
          }
        }
      }

      // Description
      item {
        Card(
          modifier = Modifier.fillMaxWidth()
        ) {
          Column(
            modifier = Modifier.padding(16.dp)
          ) {
            Text(
              text = "Mô tả",
              style = MaterialTheme.typography.titleMedium,
              fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
              text = property!!.description,
              style = MaterialTheme.typography.bodyMedium
            )
          }
        }
      }

      // Amenities
      item {
        Card {
          Column(
            modifier = Modifier.padding(16.dp).fillMaxWidth()
          ) {
            Text(
              text = "Tiện ích",
              style = MaterialTheme.typography.titleMedium,
              fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))

            property!!.amenities.forEach { amenity ->
              Row(
                modifier = Modifier.padding(vertical = 2.dp),
                verticalAlignment = Alignment.CenterVertically
              ) {
                Text(
                  text = "• ",
                  color = MaterialTheme.colorScheme.primary,
                  fontWeight = FontWeight.Bold
                )
                Text(
                  text = amenity,
                  style = MaterialTheme.typography.bodyMedium
                )
              }
            }
          }
        }
      }

      // Contact info
      item {
        Card {
          Column(
            modifier = Modifier.padding(16.dp).fillMaxWidth()
          ) {
            Text(
              text = "Thông tin liên hệ",
              style = MaterialTheme.typography.titleMedium,
              fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))

            Text(
              text = "Chủ nhà: ${property!!.contactInfo.ownerName}",
              style = MaterialTheme.typography.bodyMedium
            )
            Text(
              text = "Điện thoại: ${property!!.contactInfo.phoneNumber}",
              style = MaterialTheme.typography.bodyMedium
            )
            property!!.contactInfo.email?.let { email ->
              Text(
                text = "Email: $email",
                style = MaterialTheme.typography.bodyMedium
              )
            }
          }
        }
      }
    }
  }
}

@Composable
private fun DetailItem(
  icon: ImageVector,
  label: String,
  value: String
) {
  Column(
    horizontalAlignment = Alignment.CenterHorizontally
  ) {
    Icon(
      imageVector = icon,
      contentDescription = label,
      tint = MaterialTheme.colorScheme.primary,
      modifier = Modifier.size(24.dp)
    )
    Spacer(modifier = Modifier.height(4.dp))
    Text(
      text = value,
      style = MaterialTheme.typography.titleMedium,
      fontWeight = FontWeight.Bold
    )
    Text(
      text = label,
      style = MaterialTheme.typography.bodySmall,
      color = MaterialTheme.colorScheme.onSurfaceVariant
    )
  }
}

private fun getPropertyTypeText(type: PropertyType): String {
  return when (type) {
    PropertyType.APARTMENT -> "Căn hộ"
    PropertyType.HOUSE -> "Nhà"
    PropertyType.ROOM -> "Phòng trọ"
    PropertyType.STUDIO -> "Studio"
    PropertyType.VILLA -> "Biệt thự"
  }
}

private fun getRentalTypeText(type: com.jobudget.rentahub.data.models.RentalType): String {
  return when (type) {
    com.jobudget.rentahub.data.models.RentalType.MONTHLY -> "tháng"
    com.jobudget.rentahub.data.models.RentalType.YEARLY -> "năm"
    com.jobudget.rentahub.data.models.RentalType.DAILY -> "ngày"
  }
}

private fun formatPrice(price: Double): String {
  // Simple formatting for multiplatform compatibility
  val priceString = price.toLong().toString()
  val reversed = priceString.reversed()
  val formatted = reversed.chunked(3).joinToString(".").reversed()
  return "$formatted ₫"
}
