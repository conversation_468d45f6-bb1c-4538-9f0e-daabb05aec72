package com.jobudget.rentahub.ui.theme

import androidx.compose.ui.graphics.Color

// Airbnb-inspired color palette
object RentaHubColors {
    // Primary colors - Airbnb's signature red/coral
    val Primary = Color(0xFFFF5A5F) // Airbnb red
    val PrimaryVariant = Color(0xFFE04348)
    val PrimaryLight = Color(0xFFFF8A8F)
    val PrimaryDark = Color(0xFFCC2429)
    
    // Secondary colors - Teal accent
    val Secondary = Color(0xFF00A699) // Airbnb teal
    val SecondaryVariant = Color(0xFF008B7F)
    val SecondaryLight = Color(0xFF33B8AC)
    val SecondaryDark = Color(0xFF006B61)
    
    // Background colors
    val Background = Color(0xFFFFFBFF)
    val Surface = Color(0xFFFFFFFF)
    val SurfaceVariant = Color(0xFFF7F7F7)
    val SurfaceContainer = Color(0xFFF5F5F5)
    
    // Text colors
    val OnPrimary = Color(0xFFFFFFFF)
    val OnSecondary = Color(0xFFFFFFFF)
    val OnBackground = Color(0xFF222222) // Airbnb's dark gray
    val OnSurface = Color(0xFF222222)
    val OnSurfaceVariant = Color(0xFF717171) // Medium gray
    
    // Neutral colors
    val Gray50 = Color(0xFFF9F9F9)
    val Gray100 = Color(0xFFF0F0F0)
    val Gray200 = Color(0xFFE0E0E0)
    val Gray300 = Color(0xFFCCCCCC)
    val Gray400 = Color(0xFF999999)
    val Gray500 = Color(0xFF717171)
    val Gray600 = Color(0xFF555555)
    val Gray700 = Color(0xFF333333)
    val Gray800 = Color(0xFF222222)
    val Gray900 = Color(0xFF111111)
    
    // Semantic colors
    val Success = Color(0xFF00A699)
    val Warning = Color(0xFFFFB400)
    val Error = Color(0xFFFF5A5F)
    val Info = Color(0xFF0070F3)
    
    // Card and elevation colors
    val CardBackground = Color(0xFFFFFFFF)
    val CardBorder = Color(0xFFE0E0E0)
    val Divider = Color(0xFFE8E8E8)
    
    // Special colors
    val Overlay = Color(0x80000000)
    val Shimmer = Color(0xFFF0F0F0)
    val Rating = Color(0xFFFFB400) // Star rating color
}

// Light color scheme
val LightColorScheme = androidx.compose.material3.lightColorScheme(
    primary = RentaHubColors.Primary,
    onPrimary = RentaHubColors.OnPrimary,
    primaryContainer = RentaHubColors.PrimaryLight,
    onPrimaryContainer = RentaHubColors.PrimaryDark,
    
    secondary = RentaHubColors.Secondary,
    onSecondary = RentaHubColors.OnSecondary,
    secondaryContainer = RentaHubColors.SecondaryLight,
    onSecondaryContainer = RentaHubColors.SecondaryDark,
    
    background = RentaHubColors.Background,
    onBackground = RentaHubColors.OnBackground,
    
    surface = RentaHubColors.Surface,
    onSurface = RentaHubColors.OnSurface,
    surfaceVariant = RentaHubColors.SurfaceVariant,
    onSurfaceVariant = RentaHubColors.OnSurfaceVariant,
    
    error = RentaHubColors.Error,
    onError = RentaHubColors.OnPrimary,
    
    outline = RentaHubColors.Gray300,
    outlineVariant = RentaHubColors.Gray200
)

// Dark color scheme (for future use)
val DarkColorScheme = androidx.compose.material3.darkColorScheme(
    primary = RentaHubColors.PrimaryLight,
    onPrimary = RentaHubColors.Gray900,
    primaryContainer = RentaHubColors.PrimaryDark,
    onPrimaryContainer = RentaHubColors.PrimaryLight,
    
    secondary = RentaHubColors.SecondaryLight,
    onSecondary = RentaHubColors.Gray900,
    secondaryContainer = RentaHubColors.SecondaryDark,
    onSecondaryContainer = RentaHubColors.SecondaryLight,
    
    background = RentaHubColors.Gray900,
    onBackground = RentaHubColors.Gray100,
    
    surface = RentaHubColors.Gray800,
    onSurface = RentaHubColors.Gray100,
    surfaceVariant = RentaHubColors.Gray700,
    onSurfaceVariant = RentaHubColors.Gray400,
    
    error = RentaHubColors.Error,
    onError = RentaHubColors.OnPrimary,
    
    outline = RentaHubColors.Gray600,
    outlineVariant = RentaHubColors.Gray700
)
