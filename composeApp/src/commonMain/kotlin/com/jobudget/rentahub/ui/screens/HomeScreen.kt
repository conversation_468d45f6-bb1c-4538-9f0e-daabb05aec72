package com.jobudget.rentahub.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.material3.carousel.CarouselState
import androidx.compose.material3.carousel.HorizontalUncontainedCarousel
import androidx.compose.material3.carousel.rememberCarouselState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.jobudget.rentahub.data.models.SampleProperties
import com.jobudget.rentahub.ui.components.PropertyCard
import com.jobudget.rentahub.ui.components.BottomNavigationBar
import com.jobudget.rentahub.ui.theme.RentaHubColors
import com.jobudget.rentahub.ui.theme.RentaHubShapes
import com.jobudget.rentahub.ui.theme.RentaHubTypography
import com.jobudget.rentahub.ui.theme.rentaHub

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
  onNavigateToPropertyDetail: (String) -> Unit
) {
  val theme = MaterialTheme.rentaHub

  Scaffold(

    topBar = {
      TopAppBar(
        modifier = Modifier.blur(
          20.dp
        ),
        title = {
          Text(
            text = "RentaHub",
            style = MaterialTheme.typography.headlineSmall.copy(
              fontWeight = FontWeight.Bold,
              color = theme.colors.Primary
            )
          )
        },
        colors = TopAppBarDefaults.topAppBarColors(
          containerColor = theme.colors.Surface,
          scrolledContainerColor = theme.colors.Surface.copy(alpha = 0.5f),
        )
      )
    },
    containerColor = theme.colors.Surface
  ) { paddingValues ->
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
        .padding(horizontal = theme.spacing.medium),
      verticalArrangement = Arrangement.spacedBy(theme.spacing.medium),
      contentPadding = PaddingValues(vertical = theme.spacing.medium)
    ) {
      item {
        // Header section (Airbnb style)
        Column {
          Text(
            text = "Tìm nhà thuê hoàn hảo",
            style = MaterialTheme.typography.headlineMedium.copy(
              fontWeight = FontWeight.Bold,
              color = theme.colors.OnBackground
            )
          )
          Spacer(modifier = Modifier.height(theme.spacing.xs))
          Text(
            text = "Khám phá hàng nghìn bất động sản cho thuê",
            style = MaterialTheme.typography.bodyLarge.copy(
              color = theme.colors.Gray600
            )
          )
        }
      }

      item {
        // Property types carousel
        HorizontalUncontainedCarousel(
          state = rememberCarouselState { PropertyType.entries.size },
          itemWidth = 150.dp,
          itemSpacing = 16.dp
        ) { index ->
          val propertyType = PropertyType.entries[index]
          PropertyTypeCard(
            propertyType = propertyType,
            modifier = Modifier.width(150.dp)
          )
        }
      }

      item {
        Text(
          text = "Bất động sản nổi bật",
          style = MaterialTheme.typography.titleLarge.copy(
            fontWeight = FontWeight.Bold,
            color = theme.colors.OnBackground
          )
        )
      }

      items(SampleProperties.properties) { property ->
        PropertyCard(
          property = property,
          onClick = { onNavigateToPropertyDetail(property.id) },
          modifier = Modifier.padding(vertical = theme.spacing.xs)
        )
      }
    }
  }
}

enum class PropertyType(val displayName: String, val imageUrl: String) {
  APARTMENT(
    "Căn hộ",
    "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
  ),
  HOUSE(
    "Nhà ở",
    "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
  ),
  SHOPHOUSE(
    "Shophouse",
    "https://images.unsplash.com/photo-1613977257363-27411ec86278?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
  ), // Placeholder, find a better shophouse image
  VILLA(
    "Biệt thự",
    "https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
  ),
  OFFICE(
    "Văn phòng",
    "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
  ),
}

@Composable
private fun PropertyTypeCard(
  propertyType: PropertyType,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = Modifier.fillMaxSize().height(250.dp),
  ) {
    // Use an AsyncImage composable (e.g., from Coil library) to load images from URLs
    AsyncImage( // Replace with AsyncImage if using Coil or a similar library
      model = propertyType.imageUrl,
      contentDescription = propertyType.displayName,
      modifier = Modifier.fillMaxSize(), // Adjust image size
      contentScale = ContentScale.FillHeight // Crop to fill the space
    )
    Text(
      text = propertyType.displayName,
      modifier = Modifier
        .align(Alignment.BottomCenter)
        .fillMaxWidth()
        .background(
          Brush.verticalGradient(
            listOf(
              Color.Transparent,
              RentaHubColors.Gray800.copy(alpha = 0.7f)
            )
          )
        )
        .padding(bottom = 8.dp, start = 8.dp),
      style = RentaHubTypography.titleMedium.copy(
        fontWeight = FontWeight.SemiBold,
        color = Color.White
      )
    )
  }
}

@Composable
private fun QuickStatCard( // Keep this if you still need it elsewhere, or remove if not
  title: String,
  value: String,
  theme: com.jobudget.rentahub.ui.theme.RentaHubTheme,
  modifier: Modifier = Modifier
) {
  Card(
    modifier = modifier,
    colors = CardDefaults.cardColors(
      containerColor = theme.colors.Surface
    ),
    shape = theme.customShapes.PropertyCard,
    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
  ) {
    Column(
      modifier = Modifier.padding(theme.spacing.medium),
      horizontalAlignment = Alignment.CenterHorizontally
    ) {
      Text(
        text = value,
        style = MaterialTheme.typography.headlineSmall.copy(
          fontWeight = FontWeight.Bold,
          color = theme.colors.Primary
        )
      )
      Text(
        text = title,
        style = MaterialTheme.typography.bodyMedium.copy(
          color = theme.colors.Gray600
        )
      )
    }
  }
}
