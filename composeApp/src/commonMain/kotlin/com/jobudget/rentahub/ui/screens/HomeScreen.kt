package com.jobudget.rentahub.ui.screens

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.material3.carousel.CarouselState
import androidx.compose.material3.carousel.HorizontalUncontainedCarousel
import androidx.compose.material3.carousel.rememberCarouselState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.jobudget.rentahub.data.models.SampleProperties
import com.jobudget.rentahub.data.models.PropertyType as DataPropertyType
import com.jobudget.rentahub.ui.components.PropertyCard
import com.jobudget.rentahub.ui.components.BottomNavigationBar
import com.jobudget.rentahub.ui.theme.RentaHubColors
import com.jobudget.rentahub.ui.theme.RentaHubShapes
import com.jobudget.rentahub.ui.theme.RentaHubTypography
import com.jobudget.rentahub.ui.theme.rentaHub

@OptIn(ExperimentalMaterial3Api::class, ExperimentalSharedTransitionApi::class)
@Composable
fun HomeScreen(
  onNavigateToPropertyDetail: (String) -> Unit,
  onNavigateToPropertyType: (DataPropertyType) -> Unit = {},
  sharedTransitionScope: SharedTransitionScope,
  animatedContentScope: AnimatedContentScope
) {
  val theme = MaterialTheme.rentaHub

  Scaffold(

    topBar = {
      TopAppBar(
        title = {
          Text(
            text = "RentaHub",

            style = MaterialTheme.typography.headlineSmall.copy(
              fontWeight = FontWeight.Bold,
              color = theme.colors.Primary
            )
          )
        },
        colors = TopAppBarDefaults.topAppBarColors(
          containerColor = theme.colors.Surface,
          scrolledContainerColor = theme.colors.Surface.copy(alpha = 0.5f),
        )
      )
    },
    containerColor = theme.colors.Surface
  ) { paddingValues ->
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
        .padding(horizontal = theme.spacing.medium),
      verticalArrangement = Arrangement.spacedBy(theme.spacing.medium),
      contentPadding = PaddingValues(vertical = theme.spacing.medium)
    ) {
      item {
        // Header section (Airbnb style)
        Column {
          Text(
            text = "Tìm nhà thuê hoàn hảo",
            style = MaterialTheme.typography.headlineMedium.copy(
              fontWeight = FontWeight.Bold,
              color = theme.colors.OnBackground
            )
          )
          Spacer(modifier = Modifier.height(theme.spacing.xs))
          Text(
            text = "Khám phá hàng nghìn bất động sản cho thuê",
            style = MaterialTheme.typography.bodyLarge.copy(
              color = theme.colors.Gray600
            )
          )
        }
      }

      item {
        // Property types carousel
        HorizontalUncontainedCarousel(
          state = rememberCarouselState { DataPropertyType.entries.size },
          itemWidth = 150.dp,
          itemSpacing = 8.dp
        ) { index ->
          val propertyType = DataPropertyType.entries[index]
          PropertyTypeCard(
            propertyType = propertyType,
            onClick = { onNavigateToPropertyType(propertyType) },
            sharedTransitionScope = sharedTransitionScope,
            animatedContentScope = animatedContentScope,
          )
        }
      }

      item {
        Text(
          text = "Bất động sản nổi bật",
          style = MaterialTheme.typography.titleLarge.copy(
            fontWeight = FontWeight.Bold,
            color = theme.colors.OnBackground
          )
        )
      }

      items(SampleProperties.properties) { property ->
        PropertyCard(
          property = property,
          onClick = { onNavigateToPropertyDetail(property.id) },
          modifier = Modifier.padding(vertical = theme.spacing.xs)
        )
      }
    }
  }
}



@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun PropertyTypeCard(
  propertyType: DataPropertyType,
  onClick: () -> Unit = {},
  sharedTransitionScope: SharedTransitionScope,
  animatedContentScope: AnimatedContentScope,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier
      .fillMaxSize()
      .height(250.dp)
      .clickable { onClick() },
  ) {
    // Use an AsyncImage composable (e.g., from Coil library) to load images from URLs
    with(sharedTransitionScope) {
      AsyncImage( // Replace with AsyncImage if using Coil or a similar library
        model = propertyType.imageUrl,
        contentDescription = propertyType.displayName,
        modifier = Modifier
          .fillMaxSize()
          .sharedElement(
            sharedContentState = rememberSharedContentState(key = "property_type_image_${propertyType.name}"),
            animatedVisibilityScope = animatedContentScope
          ), // Adjust image size
        contentScale = ContentScale.FillHeight,// Crop to fill the space
        onError =  {
          println("Error loading image: ${it.result.throwable}")
        }
      )
    }
    // Gradient overlay
    with(sharedTransitionScope) {
      Box(
        modifier = Modifier
          .fillMaxSize()
          .background(
            Brush.verticalGradient(
              listOf(
                Color.Transparent,
                Color.Black.copy(alpha = 0.6f)
              )
            )
          )
          .sharedElement(
            sharedContentState = rememberSharedContentState(key = "property_type_gradient_${propertyType.name}"),
            animatedVisibilityScope = animatedContentScope
          )
      )
    }
    with(sharedTransitionScope) {
      Text(
        text = propertyType.displayName,
        modifier = Modifier
          .align(Alignment.BottomCenter)
          .fillMaxWidth()
          .background(
            Brush.verticalGradient(
              listOf(
                Color.Transparent,
                RentaHubColors.Gray800.copy(alpha = 0.7f)
              )
            )
          )
          .sharedElement(
            sharedContentState = rememberSharedContentState(key = "property_type_title_${propertyType.name}"),
            animatedVisibilityScope = animatedContentScope
          )
          .padding(bottom = 8.dp, start = 8.dp),
        style = RentaHubTypography.titleMedium.copy(
          fontWeight = FontWeight.SemiBold,
          color = Color.White
        )
      )
    }
  }
}

@Composable
private fun QuickStatCard( // Keep this if you still need it elsewhere, or remove if not
  title: String,
  value: String,
  theme: com.jobudget.rentahub.ui.theme.RentaHubTheme,
  modifier: Modifier = Modifier
) {
  Card(
    modifier = modifier,
    colors = CardDefaults.cardColors(
      containerColor = theme.colors.Surface
    ),
    shape = theme.customShapes.PropertyCard,
    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
  ) {
    Column(
      modifier = Modifier.padding(theme.spacing.medium),
      horizontalAlignment = Alignment.CenterHorizontally
    ) {
      Text(
        text = value,
        style = MaterialTheme.typography.headlineSmall.copy(
          fontWeight = FontWeight.Bold,
          color = theme.colors.Primary
        )
      )
      Text(
        text = title,
        style = MaterialTheme.typography.bodyMedium.copy(
          color = theme.colors.Gray600
        )
      )
    }
  }
}

