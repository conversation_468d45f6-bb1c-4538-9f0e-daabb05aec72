package com.jobudget.rentahub.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.Search
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector
import com.jobudget.rentahub.ui.theme.rentaHub

data class BottomNavItem(
    val route: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector,
    val label: String
)

val bottomNavItems = listOf(
    BottomNavItem(
        route = "home",
        selectedIcon = Icons.Filled.Home,
        unselectedIcon = Icons.Outlined.Home,
        label = "Trang chủ"
    ),
    BottomNavItem(
        route = "search",
        selectedIcon = Icons.Filled.Search,
        unselectedIcon = Icons.Outlined.Search,
        label = "Tìm kiếm"
    ),
    BottomNavItem(
        route = "post_property",
        selectedIcon = Icons.Filled.Add,
        unselectedIcon = Icons.Outlined.Add,
        label = "Đăng tin"
    ),
    BottomNavItem(
        route = "profile",
        selectedIcon = Icons.Filled.Person,
        unselectedIcon = Icons.Outlined.Person,
        label = "Hồ sơ"
    )
)

@Composable
fun BottomNavigationBar(
    currentRoute: String,
    onNavigate: (String) -> Unit
) {
    val theme = MaterialTheme.rentaHub

    NavigationBar(
        containerColor = theme.colors.Surface,
        contentColor = theme.colors.Primary
    ) {
        bottomNavItems.forEach { item ->
            val isSelected = currentRoute == item.route

            NavigationBarItem(
                selected = isSelected,
                onClick = { onNavigate(item.route) },
                icon = {
                    Icon(
                        imageVector = if (isSelected) item.selectedIcon else item.unselectedIcon,
                        contentDescription = item.label,
                        tint = if (isSelected) theme.colors.Primary else theme.colors.Gray500
                    )
                },
                label = {
                    Text(
                        text = item.label,
                        style = theme.textStyles.NavigationLabel,
                        color = if (isSelected) theme.colors.Primary else theme.colors.Gray500
                    )
                },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = theme.colors.Primary,
                    selectedTextColor = theme.colors.Primary,
                    unselectedIconColor = theme.colors.Gray500,
                    unselectedTextColor = theme.colors.Gray500,
                    indicatorColor = theme.colors.PrimaryLight.copy(alpha = 0.1f)
                )
            )
        }
    }
}
