package com.jobudget.rentahub.navigation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.jobudget.rentahub.ui.screens.HomeScreen
import com.jobudget.rentahub.ui.screens.SearchScreen
import com.jobudget.rentahub.ui.screens.PropertyDetailScreen
import com.jobudget.rentahub.ui.screens.PropertyTypeScreen
import com.jobudget.rentahub.ui.screens.ProfileScreen
import com.jobudget.rentahub.ui.screens.PostPropertyScreen
import com.jobudget.rentahub.data.models.PropertyType
import com.jobudget.rentahub.ui.components.BottomNavigationBar
import com.jobudget.rentahub.ui.theme.RentaHubColors

// Định nghĩa các route
object Routes {
  const val HOME = "home"
  const val SEARCH = "search"
  const val PROPERTY_DETAIL = "property_detail/{propertyId}"
  const val PROPERTY_TYPE = "property_type/{propertyType}"
  const val PROFILE = "profile"
  const val POST_PROPERTY = "post_property"

  fun propertyDetail(propertyId: String) = "property_detail/$propertyId"
  fun propertyType(propertyType: String) = "property_type/$propertyType"
}

@Composable
fun RentaHubNavigation(
  navController: NavHostController = rememberNavController()
) {
  val navBackStackEntry by navController.currentBackStackEntryAsState()
  val currentDestination = navBackStackEntry?.destination

  // Danh sách các route có bottom navigation
  val bottomNavRoutes = listOf(Routes.HOME, Routes.SEARCH, Routes.POST_PROPERTY, Routes.PROFILE)
  val showBottomNav = currentDestination?.route in bottomNavRoutes

  Scaffold(

    containerColor =
      RentaHubColors.SurfaceContainer,
    bottomBar = {
      AnimatedVisibility(
        showBottomNav,
        enter = slideInVertically(initialOffsetY = { it }),
        exit = slideOutVertically(targetOffsetY = { it })

      ) {
        BottomNavigationBar(
          currentRoute = currentDestination?.route ?: Routes.HOME,
          onNavigate = { route ->
            navController.navigate(route) {
              // Pop up to the start destination of the graph to
              // avoid building up a large stack of destinations
              // on the back stack as users select items
              popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
              }
              // Avoid multiple copies of the same destination when
              // reselecting the same item
              launchSingleTop = true
              // Restore state when reselecting a previously selected item
              restoreState = true
            }
          }
        )
      }
    }
  ) { paddingValues ->
    NavHost(
      navController = navController,
      startDestination = Routes.HOME,
      modifier = Modifier.padding(paddingValues)

    ) {
      composable(Routes.HOME) {
        HomeScreen(
          onNavigateToPropertyDetail = { propertyId ->
            navController.navigate(Routes.propertyDetail(propertyId))
          },
          onNavigateToPropertyType = { propertyType ->
            navController.navigate(Routes.propertyType(propertyType.name))
          }
        )
      }


      composable(Routes.SEARCH) {
        SearchScreen(
          onNavigateToPropertyDetail = { propertyId ->
            navController.navigate(Routes.propertyDetail(propertyId))
          }
        )
      }


      composable(Routes.PROPERTY_DETAIL) { backStackEntry ->
        val propertyId = backStackEntry.savedStateHandle["propertyId"] ?: ""
        PropertyDetailScreen(
          propertyId = propertyId,
          onNavigateBack = {
            navController.popBackStack()
          }
        )
      }

      composable(Routes.PROPERTY_TYPE) { backStackEntry ->
        val propertyTypeString = backStackEntry.savedStateHandle["propertyType"] ?: ""
        val propertyType = try {
          PropertyType.valueOf(propertyTypeString)
        } catch (e: IllegalArgumentException) {
          PropertyType.APARTMENT // Default fallback
        }
        PropertyTypeScreen(
          propertyType = propertyType,
          onNavigateBack = {
            navController.popBackStack()
          },
          onNavigateToPropertyDetail = { propertyId ->
            navController.navigate(Routes.propertyDetail(propertyId))
          }
        )
      }


      composable(
        Routes.PROFILE
      ) {
        ProfileScreen(
          onNavigateToPostProperty = {
            navController.navigate(Routes.POST_PROPERTY) {
              popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
              }
              launchSingleTop = true
              restoreState = true
            }
          }
        )
      }

      composable(Routes.POST_PROPERTY) {
        PostPropertyScreen(
          onNavigateBack = {
            navController.popBackStack()
          }
        )
      }
    }
  }
}
