# RentaHub - Ứng dụng PropTech cho thuê nhà

RentaHub là một ứng dụng PropTech được xây dựng bằng Kotlin Multiplatform và Compose Multiplatform, giúp kết nối người thuê và người cho thuê bất động sản một cách dễ dàng và hiệu quả.

## 🚀 Tính năng chính

### Cho người thuê:
- **Tìm kiếm bất động sản**: Duy<PERSON>t qua hàng nghìn tin đăng cho thuê
- **Bộ lọc thông minh**: <PERSON><PERSON><PERSON> theo lo<PERSON>h<PERSON>, gi<PERSON>, di<PERSON>n tích, số phòng
- **Xem chi tiết**: Thông tin đầy đủ về bất động sản, hình ảnh, tiện ích
- **Liên hệ trực tiếp**: G<PERSON>i điện hoặc nhắn tin với chủ nhà
- **Lưu yêu thích**: Bookmark các bất động sản quan tâm

### Cho người cho thuê:
- **Đăng tin dễ dàng**: Giao diện đơn giản để đăng tin cho thuê
- **Quản lý tin đăng**: Theo dõi và chỉnh sửa các tin đã đăng
- **Thông tin liên hệ**: Nhận liên hệ từ người thuê tiềm năng

## 🏗️ Kiến trúc ứng dụng

### Tech Stack:
- **Kotlin Multiplatform**: Chia sẻ logic business giữa Android và iOS
- **Compose Multiplatform**: UI framework hiện đại cho cả hai platform
- **Navigation Compose**: Điều hướng giữa các màn hình
- **Material 3**: Design system hiện đại của Google

### Cấu trúc dự án:
```
composeApp/src/
├── commonMain/kotlin/com/jobudget/rentahub/
│   ├── data/models/          # Data models (Property, User)
│   ├── navigation/           # Navigation setup
│   ├── ui/
│   │   ├── components/       # Reusable UI components
│   │   └── screens/          # Screen composables
│   └── App.kt               # Main app entry point
├── androidMain/             # Android-specific code
└── iosMain/                 # iOS-specific code
```

## 📱 Màn hình ứng dụng

1. **Màn hình chính (HomeScreen)**: Hiển thị danh sách bất động sản nổi bật
2. **Màn hình tìm kiếm (SearchScreen)**: Tìm kiếm và lọc bất động sản
3. **Màn hình chi tiết (PropertyDetailScreen)**: Thông tin chi tiết bất động sản
4. **Màn hình hồ sơ (ProfileScreen)**: Quản lý thông tin cá nhân
5. **Màn hình đăng tin (PostPropertyScreen)**: Đăng tin cho thuê mới

## 🛠️ Cài đặt và chạy ứng dụng

### Yêu cầu hệ thống:
- JDK 11 hoặc cao hơn
- Android Studio (cho Android)
- Xcode (cho iOS, chỉ trên macOS)

### Chạy ứng dụng Android:
```bash
./gradlew composeApp:assembleDebug
```

### Chạy ứng dụng iOS:
```bash
./gradlew composeApp:iosSimulatorArm64Test
```

## 🎨 Thiết kế UI

Ứng dụng sử dụng Material 3 Design System với:
- **Màu sắc**: Palette hiện đại với primary color xanh dương
- **Typography**: Roboto font family
- **Components**: Card, Button, TextField, Navigation Bar
- **Layout**: Responsive design cho nhiều kích thước màn hình

## 📊 Quản lý State

Ứng dụng sử dụng Compose State management đơn giản:
- **remember**: Lưu trữ state local trong composable
- **mutableStateOf**: Reactive state cho UI updates
- **Không sử dụng ViewModel**: Giữ cho architecture đơn giản

## 🔄 Navigation

Sử dụng Navigation Compose với:
- **Routes**: Định nghĩa các đường dẫn màn hình
- **Arguments**: Truyền dữ liệu giữa màn hình
- **Back Stack**: Quản lý navigation stack

## 📝 Dữ liệu mẫu

Ứng dụng hiện tại sử dụng dữ liệu mẫu (mock data):
- **SampleProperties**: Danh sách bất động sản mẫu
- **SampleUser**: Thông tin người dùng mẫu

## 🚧 Tính năng sắp tới

- [ ] Tích hợp API backend
- [ ] Xác thực người dùng
- [ ] Upload hình ảnh
- [ ] Thông báo push
- [ ] Chat trong ứng dụng
- [ ] Bản đồ tích hợp
- [ ] Thanh toán online

## 🤝 Đóng góp

Chào mừng mọi đóng góp! Vui lòng:
1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📄 License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## 🔧 Cấu trúc code chi tiết

### Models (data/models/):
- `Property.kt`: Định nghĩa model bất động sản với các thuộc tính như title, price, address, amenities
- `User.kt`: Model người dùng với phân loại TENANT/LANDLORD

### Navigation (navigation/):
- `RentaHubNavigation.kt`: Setup navigation graph với các routes và parameter passing

### UI Components (ui/components/):
- `PropertyCard.kt`: Component hiển thị thông tin bất động sản dạng card
- `BottomNavigationBar.kt`: Thanh navigation dưới cùng

### Screens (ui/screens/):
- `HomeScreen.kt`: Màn hình chính với danh sách bất động sản
- `SearchScreen.kt`: Màn hình tìm kiếm với bộ lọc
- `PropertyDetailScreen.kt`: Màn hình chi tiết bất động sản
- `ProfileScreen.kt`: Màn hình hồ sơ người dùng
- `PostPropertyScreen.kt`: Màn hình đăng tin cho thuê

## 📋 Hướng dẫn phát triển

### Thêm màn hình mới:
1. Tạo file Composable trong `ui/screens/`
2. Thêm route mới trong `Routes` object
3. Cập nhật `RentaHubNavigation.kt`
4. Thêm navigation logic trong các màn hình liên quan

### Thêm component mới:
1. Tạo file trong `ui/components/`
2. Implement Composable function
3. Sử dụng trong các screen cần thiết

### Cập nhật data model:
1. Chỉnh sửa file trong `data/models/`
2. Cập nhật sample data nếu cần
3. Kiểm tra impact trên UI components

## 🐛 Troubleshooting

### Build errors:
- Đảm bảo JDK version đúng (11+)
- Clean và rebuild project: `./gradlew clean build`
- Kiểm tra dependencies trong `build.gradle.kts`

### Navigation issues:
- Kiểm tra route definitions trong `Routes` object
- Verify parameter passing syntax
- Debug navigation stack với logs

### UI rendering problems:
- Kiểm tra Compose version compatibility
- Verify Material 3 components usage
- Test trên nhiều screen sizes
